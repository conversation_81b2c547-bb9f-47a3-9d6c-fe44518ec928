// ===================================================================================
// Clash Verge Rev 全局脚本 - 修复版本
// 适配用户现有配置，不覆盖策略组
// ===================================================================================

// ===================================================================================
// 1. 节点过滤配置区 (管理要排除的节点关键字)
// ===================================================================================

const EXCLUDED_KEYWORDS = [
    "剩余流量", "到期时间", "版本", "官网", "产品", "平台", "关注", "联系", "QQ", "群", "频道", "订阅", "ISP", "流量", "官方", "套餐"
];

// ===================================================================================
// 2. 自定义规则配置区 (管理自定义分流规则)
// ===================================================================================

const CustomizationRule = [
    "DOMAIN-SUFFIX,jetbrains.ai,⚙️ 节点选择",
    "PROCESS-NAME,tailscaled,DIRECT",
    "PROCESS-NAME,tailscaled.exe,DIRECT",
    "DOMAIN-SUFFIX,mcdn.bilivideo.com,REJECT",
    "DOMAIN-SUFFIX,mcdn.bilivideo.cn,REJECT",
    "DOMAIN-SUFFIX,szbdyd.com,REJECT",
];

// ===================================================================================
// 3. 服务配置区 (管理各种服务的分流策略)
// ===================================================================================

const ENABLED_SERVICES = {
    // AI服务
    'openai': {
        enabled: true,
        allowDirect: false,
        groupName: '💸 ChatGPT-Gemini-XAI-Perplexity',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/chatgpt.svg',
        regions: [],
        rule: {
            providerKey: 'openai',
            url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/OpenAI/OpenAI.yaml'
        }
    },
    'anthropic': {
        enabled: true,
        allowDirect: false,
        groupName: '💵 Claude',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/chatgpt.svg',
        regions: [],
        rule: {
            providerKey: 'anthropic',
            url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Claude/Claude.yaml'
        }
    },
    'google-gemini': {
        enabled: true,
        allowDirect: false,
        groupName: '💸 ChatGPT-Gemini-XAI-Perplexity',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/chatgpt.svg',
        regions: [],
        rule: {
            providerKey: 'google-gemini',
            url: 'https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/google-gemini.yaml'
        }
    },
    'xai': {
        enabled: true,
        allowDirect: false,
        groupName: '💸 ChatGPT-Gemini-XAI-Perplexity',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/chatgpt.svg',
        regions: [],
        rule: {
            providerKey: 'xai',
            url: 'https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/xai.yaml'
        }
    },
    'perplexity': {
        enabled: true,
        allowDirect: false,
        groupName: '💸 ChatGPT-Gemini-XAI-Perplexity',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/chatgpt.svg',
        regions: [],
        rule: {
            providerKey: 'perplexity',
            url: 'https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/perplexity.yaml'
        }
    },

    // 通讯服务
    'telegram': {
        enabled: true,
        allowDirect: false,
        groupName: '📲 电报消息',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/telegram.svg',
        regions: [],
        rule: {
            providerKey: 'telegram',
            url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Telegram/Telegram.yaml'
        }
    },

    // 科技服务
    'google': {
        enabled: true,
        allowDirect: false,
        groupName: '📢 谷歌服务',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/google.svg',
        regions: [],
        rule: {
            providerKey: 'google',
            url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Google/Google.yaml'
        }
    },
    'microsoft': {
        enabled: true,
        allowDirect: true,
        groupName: 'Ⓜ️ 微软服务',
        icon: 'https://www.clashverge.dev/assets/icons/microsoft.svg',
        regions: [],
        rule: {
            providerKey: 'microsoft',
            url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Microsoft/Microsoft.yaml'
        }
    },
    'apple': {
        enabled: true,
        allowDirect: true,
        groupName: '🍎 苹果服务',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/apple.svg',
        regions: [],
        rule: {
            providerKey: 'apple',
            url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Apple/Apple_Classical.yaml'
        }
    },

    // 其他服务
    'pikpak': {
        enabled: true,
        allowDirect: false,
        groupName: '🅿️ PikPak',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/link.svg',
        regions: [],
        rule: {
            providerKey: 'pikpak',
            url: 'https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/pikpak.yaml'
        }
    },
    'bybit': {
        enabled: true,
        allowDirect: false,
        groupName: '🪙 Bybit',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/link.svg',
        regions: [],
        rule: {
            providerKey: 'bybit',
            url: 'https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/bybit.yaml'
        }
    },

    // 禁用的服务
    'github': {
        enabled: false,
        allowDirect: false,
        groupName: 'Github',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/github.svg',
        regions: [],
        rule: {
            providerKey: 'github',
            url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/GitHub/GitHub.yaml'
        }
    },
    'netflix': {
        enabled: false,
        allowDirect: false,
        groupName: 'Netflix',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/netflix.svg',
        regions: ['HK'],
        rule: {
            providerKey: 'netflix',
            url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Netflix/Netflix_Classical.yaml'
        }
    }
};

// ===================================================================================
// 4. 区域枚举配置区 (管理节点地区) - 保留但不使用
// ===================================================================================

const REGIONS = {
    "HK": {
        "name": '香港',
        "regex": /香港|HK|Hong|🇭🇰/,
        "icon": 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/flags/hk.svg'
    },
    "TW": {
        "name": '台湾',
        "regex": /台湾|TW|Taiwan|🇹🇼/,
        "icon": 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/flags/tw.svg'
    },
    "SG": {
        "name": '新加坡',
        "regex": /新加坡|狮城|SG|Singapore|🇸🇬/,
        "icon": 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/flags/sg.svg'
    },
    "JP": {
        "name": '日本',
        "regex": /日本|JP|Japan|🇯🇵/,
        "icon": 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/flags/jp.svg'
    },
    "US": {
        "name": '美国',
        "regex": /美国|US|United States|America|🇺🇸/,
        "icon": 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/flags/us.svg'
    },
    "KR": {
        "name": '韩国',
        "regex": /韩国|KR|Korea|🇰🇷/,
        "icon": 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/flags/kr.svg'
    }
};

// ===================================================================================
// 5. 静态配置区 (管理固定的规则和DNS配置)
// ===================================================================================

const staticRules = {
    "top": [
        ...CustomizationRule,
        "RULE-SET,applications,DIRECT",
        "RULE-SET,private,DIRECT",
        "RULE-SET,reject,🥰 广告过滤"
    ],
    "bottom": [
        "RULE-SET,proxy,🔰 模式选择",
        "RULE-SET,gfw,🔰 模式选择",
        "RULE-SET,direct,DIRECT,no-resolve",
        "RULE-SET,lancidr,DIRECT,no-resolve",
        "RULE-SET,cncidr,DIRECT,no-resolve",
        "GEOIP,LAN,DIRECT,no-resolve",
        "GEOIP,CN,DIRECT,no-resolve",
        "MATCH,🐟 漏网之鱼"
    ]
};

const staticRuleProviders = {
    reject: {
        type: "http",
        behavior: "domain",
        url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/reject.txt",
        path: "./ruleset/reject.yaml",
        interval: 86400
    },
    icloud: {
        type: "http",
        behavior: "domain",
        url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/icloud.txt",
        path: "./ruleset/icloud.yaml",
        interval: 86400
    },
    apple: {
        type: "http",
        behavior: "domain",
        url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/apple.txt",
        path: "./ruleset/apple.yaml",
        interval: 86400
    },
    google: {
        type: "http",
        behavior: "domain",
        url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/google.txt",
        path: "./ruleset/google.yaml",
        interval: 86400
    },
    proxy: {
        type: "http",
        behavior: "domain",
        url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/proxy.txt",
        path: "./ruleset/proxy.yaml",
        interval: 86400
    },
    direct: {
        type: "http",
        behavior: "domain",
        url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/direct.txt",
        path: "./ruleset/direct.yaml",
        interval: 86400
    },
    private: {
        type: "http",
        behavior: "domain",
        url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/private.txt",
        path: "./ruleset/private.yaml",
        interval: 86400
    },
    gfw: {
        type: "http",
        behavior: "domain",
        url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/gfw.txt",
        path: "./ruleset/gfw.yaml",
        interval: 86400
    },
    "tld-not-cn": {
        type: "http",
        behavior: "domain",
        url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/tld-not-cn.txt",
        path: "./ruleset/tld-not-cn.yaml",
        interval: 86400
    },
    telegramcidr: {
        type: "http",
        behavior: "ipcidr",
        url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/telegramcidr.txt",
        path: "./ruleset/telegramcidr.yaml",
        interval: 86400
    },
    cncidr: {
        type: "http",
        behavior: "ipcidr",
        url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/cncidr.txt",
        path: "./ruleset/cncidr.yaml",
        interval: 86400
    },
    lancidr: {
        type: "http",
        behavior: "ipcidr",
        url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/lancidr.txt",
        path: "./ruleset/lancidr.yaml",
        interval: 86400
    },
    applications: {
        type: "http",
        behavior: "classical",
        url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/applications.txt",
        path: "./ruleset/applications.yaml",
        interval: 86400
    }
};

const ruleProviderCommon = {
    "type": "http",
    "interval": 86400
};

const dnsConfig = {
    "enable": true,
    "listen": "0.0.0.0:1053",
    "ipv6": true,
    "use-hosts": true,
    "cache-algorithm": "arc",
    "enhanced-mode": "fake-ip",
    "fake-ip-range": "**********/16",
    "default-nameserver": ["*********", "*******"],
    "nameserver": ["https://*********/dns-query", "https://*******/dns-query"],
    "nameserver-policy": {
        "geosite:private,cn,geolocation-cn": ["https://*********/dns-query", "https://**********/dns-query"],
        "geosite:google,youtube,telegram,gfw,geolocation-!cn": ["https://*******/dns-query", "https://*******/dns-query"]
    }
};

// ===================================================================================
// 6. 程序主入口 - 修复版本
// ===================================================================================

function main(config) {
    // ✅ 检查配置有效性
    if ((config?.proxies?.length ?? 0) === 0 && (typeof config?.["proxy-providers"] === "object" ? Object.keys(config["proxy-providers"]).length : 0) === 0) {
        throw new Error("配置文件中未找到任何代理");
    }

    // ✅ 保留用户现有的策略组配置，不覆盖
    // ✅ 只处理规则集和DNS配置
    const dynamicRuleProviders = {};
    const dynamicRules = [];

    // ✅ 处理启用的服务，生成规则集
    for (const serviceKey in ENABLED_SERVICES) {
        const service = ENABLED_SERVICES[serviceKey];
        if (service.enabled) {
            dynamicRuleProviders[service.rule.providerKey] = {
                ...ruleProviderCommon,
                behavior: 'classical',
                url: service.rule.url,
                path: `./ruleset/generated/${service.rule.providerKey}.yaml`
            };

            let ruleString = `RULE-SET,${service.rule.providerKey},${service.groupName}`;
            if (service.rule.options) {
                ruleString += `,${service.rule.options}`;
            }
            dynamicRules.push(ruleString);
        }
    }

    // ✅ 合并规则集配置（不覆盖现有的）
    config["rule-providers"] = {
        ...staticRuleProviders,
        ...config["rule-providers"],  // 保留现有规则集
        ...dynamicRuleProviders       // 添加新的规则集
    };

    // ✅ 合并规则（不覆盖现有的）
    const existingRules = config["rules"] || [];
    config["rules"] = [
        ...staticRules.top,
        ...dynamicRules,
        ...existingRules,  // 保留现有规则
        ...staticRules.bottom
    ];

    // ✅ 优化DNS配置（可选，保留用户现有DNS配置）
    if (!config["dns"] || Object.keys(config["dns"]).length === 0) {
        config["dns"] = dnsConfig;
    } else {
        // 只更新部分DNS配置，保留用户自定义部分
        config["dns"]["nameserver-policy"] = {
            ...config["dns"]["nameserver-policy"],
            ...dnsConfig["nameserver-policy"]
        };
    }

    // ✅ 为现有代理节点添加UDP支持
    if (config["proxies"]) {
        config["proxies"].forEach(proxy => {
            if (!proxy.hasOwnProperty('udp')) {
                proxy.udp = true;
            }
        });
    }

    return config;
}
