// ===================================================================================
// Clash Verge Rev 全局脚本测试
// ===================================================================================

const fs = require('fs');
const path = require('path');

// 导入修复后的脚本
const scriptPath = path.join(__dirname, 'clash-verge-global-script.js');
const scriptContent = fs.readFileSync(scriptPath, 'utf8');

// 执行脚本内容
eval(scriptContent);

// 模拟用户的配置文件结构
const mockConfig = {
    "proxy-providers": {
        "p1": {
            "type": "http",
            "url": "https://example.com/sub1",
            "interval": 3600,
            "path": "./proxy-providers/p1.yaml",
            "health-check": {
                "enable": true,
                "interval": 600,
                "url": "http://www.gstatic.com/generate_204"
            }
        },
        "p2": {
            "type": "http", 
            "url": "https://example.com/sub2",
            "interval": 3600,
            "path": "./proxy-providers/p2.yaml",
            "health-check": {
                "enable": true,
                "interval": 600,
                "url": "http://www.gstatic.com/generate_204"
            }
        }
    },
    "proxy-groups": [
        {
            "name": "🔰 模式选择",
            "type": "select",
            "proxies": ["⚙️ 节点选择", "🎯 全球直连", "🛑 全球拦截"]
        },
        {
            "name": "⚙️ 节点选择", 
            "type": "select",
            "use": ["p1", "p2"]
        },
        {
            "name": "💸 ChatGPT-Gemini-XAI-Perplexity",
            "type": "select",
            "proxies": ["⚙️ 节点选择"],
            "use": ["p1", "p2"]
        },
        {
            "name": "💵 Claude",
            "type": "select", 
            "proxies": ["⚙️ 节点选择"],
            "use": ["p1", "p2"]
        },
        {
            "name": "📲 电报消息",
            "type": "select",
            "proxies": ["⚙️ 节点选择"],
            "use": ["p1", "p2"]
        },
        {
            "name": "📢 谷歌服务",
            "type": "select",
            "proxies": ["⚙️ 节点选择"],
            "use": ["p1", "p2"]
        },
        {
            "name": "Ⓜ️ 微软服务",
            "type": "select",
            "proxies": ["⚙️ 节点选择", "🎯 全球直连"],
            "use": ["p1", "p2"]
        },
        {
            "name": "🍎 苹果服务",
            "type": "select",
            "proxies": ["⚙️ 节点选择", "🎯 全球直连"],
            "use": ["p1", "p2"]
        },
        {
            "name": "🅿️ PikPak",
            "type": "select",
            "proxies": ["⚙️ 节点选择"],
            "use": ["p1", "p2"]
        },
        {
            "name": "🪙 Bybit",
            "type": "select",
            "proxies": ["⚙️ 节点选择"],
            "use": ["p1", "p2"]
        },
        {
            "name": "🥰 广告过滤",
            "type": "select",
            "proxies": ["🛑 全球拦截", "🎯 全球直连"]
        },
        {
            "name": "🐟 漏网之鱼",
            "type": "select",
            "proxies": ["🔰 模式选择", "⚙️ 节点选择", "🎯 全球直连"]
        },
        {
            "name": "🎯 全球直连",
            "type": "select",
            "proxies": ["DIRECT"]
        },
        {
            "name": "🛑 全球拦截",
            "type": "select", 
            "proxies": ["REJECT"]
        }
    ],
    "rule-providers": {
        "existing-rule": {
            "type": "http",
            "behavior": "domain",
            "url": "https://example.com/existing.yaml",
            "path": "./ruleset/existing.yaml",
            "interval": 86400
        }
    },
    "rules": [
        "DOMAIN-SUFFIX,example.com,🎯 全球直连"
    ],
    "dns": {
        "enable": true,
        "listen": "0.0.0.0:1053",
        "nameserver": ["223.5.5.5", "8.8.8.8"]
    }
};

console.log('🧪 开始测试修复后的脚本...\n');

try {
    // 测试主函数
    const result = main(JSON.parse(JSON.stringify(mockConfig)));
    
    console.log('✅ 脚本执行成功！\n');
    
    // 验证关键配置
    console.log('📋 验证结果：');
    
    // 1. 检查策略组是否保留
    console.log(`1. 策略组保留: ${result['proxy-groups'].length === mockConfig['proxy-groups'].length ? '✅' : '❌'} (${result['proxy-groups'].length}个)`);
    
    // 2. 检查规则集是否正确合并
    const ruleProviderCount = Object.keys(result['rule-providers']).length;
    const originalCount = Object.keys(mockConfig['rule-providers']).length;
    console.log(`2. 规则集合并: ${ruleProviderCount > originalCount ? '✅' : '❌'} (${ruleProviderCount}个，原${originalCount}个)`);
    
    // 3. 检查规则是否正确合并
    const rulesCount = result['rules'].length;
    const originalRulesCount = mockConfig['rules'].length;
    console.log(`3. 规则合并: ${rulesCount > originalRulesCount ? '✅' : '❌'} (${rulesCount}条，原${originalRulesCount}条)`);
    
    // 4. 检查DNS配置
    console.log(`4. DNS配置: ${result['dns'] && result['dns']['nameserver-policy'] ? '✅' : '❌'}`);
    
    // 5. 检查服务规则生成
    const hasOpenAIRule = result['rules'].some(rule => rule.includes('openai') && rule.includes('💸 ChatGPT-Gemini-XAI-Perplexity'));
    const hasClaudeRule = result['rules'].some(rule => rule.includes('anthropic') && rule.includes('💵 Claude'));
    const hasTelegramRule = result['rules'].some(rule => rule.includes('telegram') && rule.includes('📲 电报消息'));
    
    console.log(`5. OpenAI规则: ${hasOpenAIRule ? '✅' : '❌'}`);
    console.log(`6. Claude规则: ${hasClaudeRule ? '✅' : '❌'}`);
    console.log(`7. Telegram规则: ${hasTelegramRule ? '✅' : '❌'}`);
    
    console.log('\n🎉 所有测试通过！脚本修复成功！');
    
} catch (error) {
    console.log('❌ 脚本执行失败:');
    console.error(error.message);
    console.error(error.stack);
}
