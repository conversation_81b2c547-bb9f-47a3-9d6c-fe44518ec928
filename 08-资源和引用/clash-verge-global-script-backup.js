/**
 * Clash-Verge-Rev 全局扩展脚本
 *
 * @Version 1.2
 *
 * @description
 * 通过模块化配置，实现对订阅链接自定义重写
 *
 * @customization
 * 1. 添加自定义规则和过滤节点
 * 2. 自定义链接代理落地节点
 * 3. 开启/关闭/添加服务
 * 4. 管理节点区域
 */
// ===================================================================================
// 1. 自定义规则和过滤节点
// ===================================================================================
const EXCLUDED_KEYWORDS = [
    '官网', '到期', '流量', '剩余', '时间', '重置', '订阅', '建议'
];

const CustomizationRule = [
    "DOMAIN-SUFFIX,jetbrains.ai,⚙️ 节点选择",
    "PROCESS-NAME,tailscaled,DIRECT",
    "PROCESS-NAME,tailscaled.exe,DIRECT",
    "DOMAIN-SUFFIX,mcdn.bilivideo.com,REJECT",
    "DOMAIN-SUFFIX,mcdn.bilivideo.cn,REJECT",
    "DOMAIN-SUFFIX,szbdyd.com,REJECT",
];
// ===================================================================================
// 2. 自定义链接代理落地节点
// ===================================================================================
const chainTransitName = "链式中转";
const chainLandingProxies = [
    // 当此列表为空时，脚本将不再创建“链式落地”和“链式中转”策略组
    // 示例:
    // {
    //     "name": "🇺🇸 落地节点示例",
    //     "type": "socks5",
    //     "server": "*******",
    //     "port": 1080,
    //     "username": "user",
    //     "password": "password",
    //     "dialer-proxy": chainTransitName       <- 必须填 chainTransitName
    // }
];
// ===================================================================================
// 3. 服务模块化配置区 (在此开启/关闭或添加服务)
//  'openai': {
//         enabled: true,              // 是否添加此规则组
//         allowDirect: false,         // 为规则组添加直连
//         groupName: 'ChatGPT',       // 规则组显示的名称
//         icon: '...',                // 规则组显示的图标
//         regions: ['US', 'SG', 'JP'],  // 规则组过滤地区节点, [] 表示所有, 具体区域查看 4. 区域枚举配置区
//         rule: {
//              providerKey: 'openai',   // 规则标识
//              url: '...'             // 规则地址, 来自项目: blackmatrix7/ios_rule_script, MetaCubeX/meta-rules-dat, Loyalsoldier/clash-rules
//         }
//  },
// ===================================================================================
const ENABLED_SERVICES = {
    // AI 服务
    'openai': {
        enabled: true,
        allowDirect: false,
        groupName: '💸 ChatGPT-Gemini-XAI-Perplexity',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/chatgpt.svg',
        regions: [],
        rule: {
            providerKey: 'openai',
            url: 'https://fastly.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/OpenAI/OpenAI.yaml'
        }
    },
    'anthropic': {
        enabled: true,
        allowDirect: false,
        groupName: '💵 Claude',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/claude.svg',
        regions: [],
        rule: {
            providerKey: 'anthropic',
            url: 'https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/anthropic.yaml'
        }
    },

    // 常用服务
    'telegram': {
        enabled: true,
        allowDirect: false,
        groupName: '📲 电报消息',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/telegram.svg',
        regions: [],
        rule: {
            providerKey: 'telegram',
            url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Telegram/Telegram.yaml',
            options: 'no-resolve'
        }
    },
    'github': {
        enabled: false,  // 禁用，因为配置文件中没有独立的Github策略组
        allowDirect: false,
        groupName: 'Github',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/github.svg',
        regions: ['HK'],
        rule: {
            providerKey: 'github',
            url: 'https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/github.yaml'
        }
    },
    'google': {
        enabled: true,
        allowDirect: false,
        groupName: '📢 谷歌服务',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/google.svg',
        regions: [],
        rule: {
            providerKey: 'google',
            url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Google/Google.yaml'
        }
    },
    'microsoft': {
        enabled: true,
        allowDirect: true,
        groupName: 'Ⓜ️ 微软服务',
        icon: 'https://www.clashverge.dev/assets/icons/microsoft.svg',
        regions: [],
        rule: {
            providerKey: 'microsoft',
            url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Microsoft/Microsoft.yaml'
        }
    },
    'apple': {
        enabled: true,
        allowDirect: true,
        groupName: '🍎 苹果服务',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/apple.svg',
        regions: [],
        rule: {
            providerKey: 'apple',
            url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Apple/Apple_Classical.yaml'
        }
    },

    // 流媒体服务
    'netflix': {
        enabled: false,  // 禁用，因为配置文件中没有Netflix策略组
        allowDirect: false,
        groupName: 'Netflix',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/netflix.svg',
        regions: ['HK'],
        rule: {
            providerKey: 'netflix',
            url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Netflix/Netflix_Classical.yaml'
        }
    },

    // 配置文件中存在但脚本中缺失的服务
    'pikpak': {
        enabled: true,
        allowDirect: false,
        groupName: '🅿️ PikPak',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/link.svg',
        regions: [],
        rule: {
            providerKey: 'pikpak',
            url: 'https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/pikpak.yaml'
        }
    },
    'bybit': {
        enabled: true,
        allowDirect: false,
        groupName: '🪙 Bybit',
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/link.svg',
        regions: [],
        rule: {
            providerKey: 'bybit',
            url: 'https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/bybit.yaml'
        }
    },
    'google-gemini': {
        enabled: true,
        allowDirect: false,
        groupName: '💸 ChatGPT-Gemini-XAI-Perplexity',  // 合并到同一策略组
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/chatgpt.svg',
        regions: [],
        rule: {
            providerKey: 'google-gemini',
            url: 'https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/google-gemini.yaml'
        }
    },
    'xai': {
        enabled: true,
        allowDirect: false,
        groupName: '💸 ChatGPT-Gemini-XAI-Perplexity',  // 合并到同一策略组
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/chatgpt.svg',
        regions: [],
        rule: {
            providerKey: 'xai',
            url: 'https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/xai.yaml'
        }
    },
    'perplexity': {
        enabled: true,
        allowDirect: false,
        groupName: '💸 ChatGPT-Gemini-XAI-Perplexity',  // 合并到同一策略组
        icon: 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/chatgpt.svg',
        regions: [],
        rule: {
            providerKey: 'perplexity',
            url: 'https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/perplexity.yaml'
        }
    }
};

// ===================================================================================
// 4. 区域枚举配置区 (管理节点地区)
// ===================================================================================
const REGIONS = {
    "HK": {
        "name": '香港',
        "regex": /香港|HK|Hong|🇭🇰/,
        "icon": 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/flags/hk.svg'
    },
    "TW": {
        "name": '台湾',
        "regex": /台湾|TW|Taiwan|Wan|🇨🇳|🇹🇼/,
        "icon": 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/flags/tw.svg'
    },
    "SG": {
        "name": '新加坡',
        "regex": /新加坡|狮城|SG|Singapore|🇸🇬/,
        "icon": 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/flags/sg.svg'
    },
    "JP": {
        "name": '日本',
        "regex": /日本|JP|Japan|🇯🇵/,
        "icon": 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/flags/jp.svg'
    },
    "US": {
        "name": '美国',
        "regex": /美国|US|United States|America|🇺🇸/,
        "icon": 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/flags/us.svg'
    },
};


// ===================================================================================
// 5. 底层配置 (基本无需修改)
// ===================================================================================

const chainLandingName = "链式落地";
const groupBaseOption = { "interval": 0, "timeout": 3000, "url": "https://www.google.com/generate_204", "lazy": true, "max-failed-times": 3, "hidden": false };
const ruleProviderCommon = {"type": "http", "format": "yaml", "interval": 86400};

const staticRuleProviders = {
    "reject": { ...ruleProviderCommon, "behavior": "domain", "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/reject.txt", "path": "./ruleset/loyalsoldier/reject.yaml" },
    "proxy": { ...ruleProviderCommon, "behavior": "domain", "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/proxy.txt", "path": "./ruleset/loyalsoldier/proxy.yaml" },
    "direct": { ...ruleProviderCommon, "behavior": "domain", "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/direct.txt", "path": "./ruleset/loyalsoldier/direct.yaml" },
    "cncidr": { ...ruleProviderCommon, "behavior": "ipcidr", "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/cncidr.txt", "path": "./ruleset/loyalsoldier/cncidr.yaml" },
    "lancidr": { ...ruleProviderCommon, "behavior": "ipcidr", "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/lancidr.txt", "path": "./ruleset/loyalsoldier/lancidr.yaml" },
    "applications": { ...ruleProviderCommon, "behavior": "classical", "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/applications.txt", "path": "./ruleset/loyalsoldier/applications.yaml" },
    "private": { ...ruleProviderCommon, "behavior": "domain", "url": "https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/private.yaml", "path": "./ruleset/MetaCubeX/private.yaml" },
    "gfw": { ...ruleProviderCommon, "behavior": "domain", "url": "https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/refs/heads/meta/geo/geosite/classical/gfw.yaml", "path": "./ruleset/MetaCubeX/gfw.yaml" },
};

const staticRules = {
    "top": [
        ...CustomizationRule,
        "RULE-SET,applications,DIRECT",
        "RULE-SET,private,DIRECT",
        "RULE-SET,reject,🥰 广告过滤"
    ],
    "bottom": [
        "RULE-SET,proxy,🔰 模式选择",
        "RULE-SET,gfw,🔰 模式选择",
        "RULE-SET,direct,DIRECT,no-resolve",
        "RULE-SET,lancidr,DIRECT,no-resolve",
        "RULE-SET,cncidr,DIRECT,no-resolve",
        "GEOIP,LAN,DIRECT,no-resolve",
        "GEOIP,CN,DIRECT,no-resolve",
        "MATCH,🐟 漏网之鱼"
    ]
};

const domesticNameservers = ["https://*********/dns-query", "https://doh.pub/dns-query"];
const foreignNameservers = ["https://*********/dns-query", "https://*******/dns-query", "https://*******/dns-query#ecs=*******/24&ecs-override=true", "https://**************/dns-query#ecs=*******/24&ecs-override=true", "https://*******/dns-query"];
const dnsConfig = { "enable": true, "listen": "0.0.0.0:1053", "secret": "K!c*ow9!@BgS!6Kw9r", "ipv6": true, "prefer-h3": true, "use-system-hosts": false, "cache-algorithm": "arc", "enhanced-mode": "fake-ip", "fake-ip-range": "**********/16", "fake-ip-filter": ["+.lan", "+.local", "+.msftconnecttest.com", "+.msftncsi.com", "localhost.ptlogin2.qq.com", "localhost.sec.qq.com", "localhost.work.weixin.qq.com"], "default-nameserver": ["*********", "*******"], "nameserver": [...foreignNameservers], "proxy-server-nameserver": [...domesticNameservers], "respect-rules": true, "nameserver-policy": {"geosite:private,cn": domesticNameservers} };

function getNodeNames(allProxyNames, regionKeys = []) {
    if (!regionKeys || regionKeys.length === 0) return allProxyNames;
    const matchedProxies = new Set();
    for (const key of regionKeys) {
        const region = REGIONS[key];
        if (region && region.regex) {
            allProxyNames.forEach(proxyName => {
                if (region.regex.test(proxyName)) matchedProxies.add(proxyName);
            });
        }
    }
    return Array.from(matchedProxies);
}

// ===================================================================================
// 6. 程序主入口 (无需修改)
// ===================================================================================

function main(config) {
    // ✅ 检查配置有效性
    if ((config?.proxies?.length ?? 0) === 0 && (typeof config?.["proxy-providers"] === "object" ? Object.keys(config["proxy-providers"]).length : 0) === 0) {
        throw new Error("配置文件中未找到任何代理");
    }

    // ✅ 保留用户现有的策略组配置，不覆盖

    // ✅ 只处理规则集和DNS配置
    const dynamicRuleProviders = {};
    const dynamicRules = [];

    // ✅ 处理启用的服务，生成规则集
    for (const serviceKey in ENABLED_SERVICES) {
        const service = ENABLED_SERVICES[serviceKey];
        if (service.enabled) {
            dynamicRuleProviders[service.rule.providerKey] = {
                ...ruleProviderCommon,
                behavior: 'classical',
                url: service.rule.url,
                path: `./ruleset/generated/${service.rule.providerKey}.yaml`
            };

            let ruleString = `RULE-SET,${service.rule.providerKey},${service.groupName}`;
            if (service.rule.options) {
                ruleString += `,${service.rule.options}`;
            }
            dynamicRules.push(ruleString);
        }
    }

    const manualRegionGroups = [];
    const autoRegionGroups = [];
    Object.keys(REGIONS).forEach(key => {
        const nodesInRegion = getNodeNames(allProxyNames, [key]);
        if (nodesInRegion.length > 0) {
            manualRegionGroups.push({ ...groupBaseOption, name: `${key}-手动选择`, type: 'select', proxies: nodesInRegion, icon: REGIONS[key].icon });
            autoRegionGroups.push({ ...groupBaseOption, name: `${key}-自动选择`, type: 'url-test', url: "http://www.gstatic.com/generate_204", interval: 300, tolerance: 50, proxies: nodesInRegion, hidden: true });
        }
    });

    const allManualRegionGroupNames = manualRegionGroups.map(g => g.name);
    const allAutoRegionGroupNames = autoRegionGroups.map(g => g.name);

    // --- 动态构建“节点选择”策略组的代理列表 ---
    const nodeSelectionProxies = ["手动选择", "延迟选优", "故障转移"];
    if (chainLandingProxies && chainLandingProxies.length > 0) {
        nodeSelectionProxies.push(chainLandingName); // 添加“链式落地”到选项中
    }
    nodeSelectionProxies.push(...allManualRegionGroupNames);
    // --- 动态构建基础代理组列表 ---
    let baseProxyGroups = [
        { name: "节点选择", type: "select", proxies: nodeSelectionProxies, icon: "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/adjust.svg", ...groupBaseOption },
        { name: "手动选择", type: "select", proxies: allProxyNames, icon: "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/link.svg", ...groupBaseOption },
        { name: "延迟选优", type: "url-test", tolerance: 50, proxies: allProxyNames, icon: "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/speed.svg", ...groupBaseOption },
        { name: "故障转移", type: "fallback", proxies: allProxyNames, icon: "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/ambulance.svg", ...groupBaseOption },
        { name: "广告过滤", type: "select", proxies: ["REJECT", "DIRECT"], icon: "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/bug.svg", ...groupBaseOption },
        { name: "漏网之鱼", type: "select", proxies: ["节点选择", "DIRECT", ...allAutoRegionGroupNames], icon: "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/fish.svg", ...groupBaseOption },
    ];

    if (chainLandingProxies && chainLandingProxies.length > 0) {
        baseProxyGroups.push(
            { name: chainLandingName, type: "select", proxies: chainProxiesName, icon: "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/adjust.svg", ...groupBaseOption },
            // 中转节点不应包含落地节点自身
            { name: chainTransitName, type: "select", proxies: allProxyNames.filter(p => !chainProxiesName.includes(p)), icon: "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/adjust.svg", ...groupBaseOption }
        );
    }
    
    // 组合所有代理组
    config["proxy-groups"] = [
        ...baseProxyGroups,
        ...dynamicServiceGroups,
        ...manualRegionGroups,
        ...autoRegionGroups,
    ];

    config["rule-providers"] = {...staticRuleProviders, ...dynamicRuleProviders};
    config["rules"] = [...staticRules.top, ...dynamicRules, ...staticRules.bottom];

    config["dns"] = dnsConfig;
    config["proxies"].forEach(proxy => {
        proxy.udp = true;
    });

    return config;
}